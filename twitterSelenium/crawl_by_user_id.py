from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
import time
from datetime import datetime
import argparse
from crawl_twitter import TwitterCrawler, convert_to_local_time, extract_tweet_id

def parse_datetime(date_str):
    """Parse datetime string in format YYYY-MM-DD HH:MM:SS"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        try:
            # Try date only format
            return datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            raise ValueError(f"Invalid datetime format: {date_str}. Use 'YYYY-MM-DD HH:MM:SS' or 'YYYY-MM-DD'")

def extract_tweet_data_from_timeline(driver, wait, start_time=None, end_time=None):
    """Extract tweet data from user timeline within time range"""
    tweets = []
    processed_tweet_ids = set()

    def safe_find_element(container, selector, multiple=False):
        """Safely find element(s) with retry logic for stale elements"""
        max_retries = 3
        for _ in range(max_retries):
            try:
                if multiple:
                    return container.find_elements(By.CSS_SELECTOR, selector)
                return container.find_element(By.CSS_SELECTOR, selector)
            except Exception:
                if _ == max_retries - 1:
                    return [] if multiple else None
                time.sleep(0.5)
        return [] if multiple else None

    try:
        # Wait for timeline to load
        print("Waiting for timeline to load...")
        try:
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="cellInnerDiv"]')))
        except:
            print("Timeline elements not found, continuing anyway...")

        # Give extra time for content to load
        time.sleep(5)

        last_height = driver.execute_script("return document.body.scrollHeight")
        max_scrolls = 20
        scrolls = 0
        consecutive_no_new_tweets = 0

        print(f"Starting to extract tweets. Initial page height: {last_height}")

        while scrolls < max_scrolls and consecutive_no_new_tweets < 3:
            # Find tweet containers - use the main selector for user timeline
            tweet_containers = driver.find_elements(By.CSS_SELECTOR, '[data-testid="cellInnerDiv"]')

            print(f"Found {len(tweet_containers)} potential tweet containers")

            new_tweets_found = 0

            for i, container in enumerate(tweet_containers):
                try:
                    # Debug first few containers
                    if i < 5:
                        print(f"Processing container {i+1}")

                    # Look for tweet text - this is the most reliable indicator
                    tweet_text_elems = safe_find_element(container, '[data-testid="tweetText"]', multiple=True)
                    if not tweet_text_elems:
                        if i < 5:
                            print(f"  No tweet text found in container {i+1}")
                        continue

                    # Get the tweet text
                    tweet_text = tweet_text_elems[0].text.strip()
                    if not tweet_text:
                        if i < 5:
                            print(f"  Empty tweet text in container {i+1}")
                        continue

                    # Find timestamp first to check if we should process this tweet
                    time_elem = safe_find_element(container, 'time')
                    if not time_elem:
                        if i < 5:
                            print(f"  No time element found in container {i+1}")
                        continue

                    utc_timestamp = time_elem.get_attribute('datetime')
                    if not utc_timestamp:
                        if i < 5:
                            print(f"  No datetime attribute in container {i+1}")
                        continue

                    local_time_str = convert_to_local_time(utc_timestamp)
                    tweet_time = datetime.strptime(local_time_str, '%Y-%m-%d %H:%M:%S')

                    # Check time range BEFORE processing further
                    if end_time and tweet_time > end_time:
                        if i < 5:
                            print(f"  Tweet too new: {tweet_time} > {end_time}")
                        continue

                    if start_time and tweet_time < start_time:
                        if i < 5:
                            print(f"  Tweet too old: {tweet_time} < {start_time}")
                        # Don't return here, just continue to next tweet
                        continue

                    # Now try to extract tweet ID
                    tweet_links = safe_find_element(container, 'a[href*="/status/"]', multiple=True)
                    if not tweet_links:
                        # Try to find the time element's parent link
                        try:
                            parent_link = time_elem.find_element(By.XPATH, './ancestor::a[contains(@href, "/status/")]')
                            tweet_links = [parent_link]
                        except:
                            if i < 5:
                                print(f"  No status link found in container {i+1}")
                            continue

                    tweet_url = tweet_links[0].get_attribute('href')
                    tweet_id = extract_tweet_id(tweet_url)

                    if not tweet_id:
                        if i < 5:
                            print(f"  Could not extract tweet ID from URL: {tweet_url}")
                        continue

                    if tweet_id in processed_tweet_ids:
                        if i < 5:
                            print(f"  Already processed tweet ID: {tweet_id}")
                        continue

                    # Build tweet data
                    tweet_data = {
                        'tweet_id': tweet_id,
                        'url': tweet_url,
                        'text': tweet_text,
                        'timestamp': local_time_str,
                        'create_time': local_time_str,
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                    # Get author info
                    author_container = safe_find_element(container, '[data-testid="User-Name"]')
                    if author_container:
                        name_elems = safe_find_element(author_container, 'span', multiple=True)
                        handle_elems = safe_find_element(author_container, 'div[dir="ltr"]', multiple=True)

                        if name_elems and handle_elems:
                            tweet_data['author'] = {
                                'name': name_elems[0].text,
                                'handle': handle_elems[0].text
                            }

                    # Get basic metrics
                    tweet_data['metrics'] = {}
                    tweet_data['has_media'] = False
                    tweet_data['media_urls'] = []

                    # Add to results
                    tweets.append(tweet_data)
                    processed_tweet_ids.add(tweet_id)
                    new_tweets_found += 1

                    if i < 5:
                        print(f"  ✓ Extracted tweet {tweet_id}: {tweet_text[:50]}...")
                    else:
                        print(f"\rTweets extracted: {len(tweets)}", end="", flush=True)

                except Exception as e:
                    if "stale element reference" not in str(e) and i < 5:
                        print(f"  Error processing container {i+1}: {e}")
                    continue

            print(f"\nFound {new_tweets_found} new tweets in this scroll")

            if new_tweets_found == 0:
                consecutive_no_new_tweets += 1
                print(f"No new tweets found. Consecutive empty scrolls: {consecutive_no_new_tweets}")
            else:
                consecutive_no_new_tweets = 0

            # Scroll down to load more content
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)

            # Check if page height changed
            new_height = driver.execute_script("return document.body.scrollHeight")
            print(f"Scroll {scrolls + 1}: height {last_height} -> {new_height}")

            if new_height == last_height:
                print("No more content to load")
                break

            last_height = new_height
            scrolls += 1

        print(f"\nSuccessfully extracted {len(tweets)} tweets")
        return tweets

    except Exception as e:
        print(f"Error extracting tweets: {e}")
        import traceback
        traceback.print_exc()
        return tweets

def crawl_user_timeline(user_id, start_time=None, end_time=None):
    """
    Crawl tweets from a user's timeline within specified time range

    Args:
        user_id (str): The user ID or username to crawl
        start_time (datetime): Start time for tweet filtering
        end_time (datetime): End time for tweet filtering

    Returns:
        list: List of tweet data dictionaries
    """
    try:
        # Get or initialize browser
        driver, wait = TwitterCrawler.get_browser()

        # Navigate to user profile
        user_url = f'https://x.com/{user_id}'
        print(f"Navigating to {user_url}")
        driver.get(user_url)
        time.sleep(5)  # Wait for content to load

        # Extract tweets from timeline
        tweets = extract_tweet_data_from_timeline(driver, wait, start_time, end_time)

        return tweets

    except Exception as e:
        print(f"Error crawling user {user_id}: {e}")
        return []

def crawl_multiple_users(user_ids, start_time=None, end_time=None):
    """
    Crawl tweets from multiple users within specified time range

    Args:
        user_ids (list): List of user IDs to crawl
        start_time (datetime): Start time for tweet filtering
        end_time (datetime): End time for tweet filtering

    Returns:
        dict: Dictionary mapping user_id to list of tweets
    """
    all_tweets = {}

    try:
        for i, user_id in enumerate(user_ids, 1):
            print(f"\n[{i}/{len(user_ids)}] Crawling user: {user_id}")
            tweets = crawl_user_timeline(user_id, start_time, end_time)
            all_tweets[user_id] = tweets
            print(f"Found {len(tweets)} tweets for user {user_id}")

            # Add delay between users to avoid rate limiting
            if i < len(user_ids):
                print("Waiting 5 seconds before next user...")
                time.sleep(5)

        return all_tweets

    except Exception as e:
        print(f"Error in crawl_multiple_users: {e}")
        return all_tweets

def main():
    parser = argparse.ArgumentParser(description='Crawl tweets from user timelines')
    parser.add_argument('--user_ids', nargs='+', required=True, help='List of user IDs to crawl')
    parser.add_argument('--start_time', type=str, help='Start time (YYYY-MM-DD HH:MM:SS or YYYY-MM-DD)')
    parser.add_argument('--end_time', type=str, help='End time (YYYY-MM-DD HH:MM:SS or YYYY-MM-DD)')
    
    args = parser.parse_args()
    
    # Parse time arguments
    start_time = None
    end_time = None
    
    if args.start_time:
        start_time = parse_datetime(args.start_time)
        print(f"Start time: {start_time}")
    
    if args.end_time:
        end_time = parse_datetime(args.end_time)
        print(f"End time: {end_time}")
    
    try:
        # Crawl tweets from all specified users
        results = crawl_multiple_users(args.user_ids, start_time, end_time)
        
        # Print summary
        total_tweets = sum(len(tweets) for tweets in results.values())
        print(f"\n=== CRAWLING SUMMARY ===")
        print(f"Users crawled: {len(args.user_ids)}")
        print(f"Total tweets found: {total_tweets}")
        for user_id, tweets in results.items():
            print(f"  {user_id}: {len(tweets)} tweets")
    
    finally:
        # Clean up browser when done
        TwitterCrawler.close_browser()

if __name__ == "__main__":
    main()
