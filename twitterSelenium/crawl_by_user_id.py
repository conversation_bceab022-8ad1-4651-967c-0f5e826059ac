from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os
import json
from datetime import datetime
from zoneinfo import ZoneInfo
import re
import argparse
from crawl_twitter import TwitterCrawler, convert_to_local_time, extract_tweet_id

def parse_datetime(date_str):
    """Parse datetime string in format YYYY-MM-DD HH:MM:SS"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        try:
            # Try date only format
            return datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            raise ValueError(f"Invalid datetime format: {date_str}. Use 'YYYY-MM-DD HH:MM:SS' or 'YYYY-MM-DD'")

def extract_tweet_data_from_timeline(driver, wait, start_time=None, end_time=None):
    """Extract tweet data from user timeline within time range"""
    tweets = []
    processed_tweet_ids = set()
    
    def safe_find_element(container, selector, multiple=False):
        """Safely find element(s) with retry logic for stale elements"""
        max_retries = 3
        for _ in range(max_retries):
            try:
                if multiple:
                    return container.find_elements(By.CSS_SELECTOR, selector)
                return container.find_element(By.CSS_SELECTOR, selector)
            except Exception:
                if _ == max_retries - 1:
                    return [] if multiple else None
                time.sleep(0.5)
        return [] if multiple else None

    try:
        # Wait for timeline to load
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="cellInnerDiv"]')))
        
        last_height = driver.execute_script("return document.body.scrollHeight")
        max_scrolls = 50  # Limit scrolls to prevent infinite loops
        scrolls = 0
        consecutive_no_new_tweets = 0
        
        while scrolls < max_scrolls and consecutive_no_new_tweets < 5:
            # Find all currently visible tweet containers
            tweet_containers = driver.find_elements(By.CSS_SELECTOR, '[data-testid="cellInnerDiv"]')
            
            new_tweets_found = 0
            
            for container in tweet_containers:
                try:
                    # Check if this is a tweet container
                    tweet_text_elems = safe_find_element(container, '[data-testid="tweetText"]', multiple=True)
                    if not tweet_text_elems:
                        continue
                    
                    # Extract tweet URL to get tweet ID
                    tweet_links = safe_find_element(container, 'a[href*="/status/"]', multiple=True)
                    if not tweet_links:
                        continue
                    
                    tweet_url = tweet_links[0].get_attribute('href')
                    tweet_id = extract_tweet_id(tweet_url)
                    
                    if not tweet_id or tweet_id in processed_tweet_ids:
                        continue
                    
                    tweet_data = {}
                    tweet_data['tweet_id'] = tweet_id
                    tweet_data['url'] = tweet_url
                    
                    # Get tweet text
                    tweet_data['text'] = tweet_text_elems[0].text
                    
                    # Get author info
                    author_container = safe_find_element(container, '[data-testid="User-Name"]')
                    if author_container:
                        name_elem = safe_find_element(author_container, 'span span', multiple=True)
                        handle_elem = safe_find_element(author_container, 'div[dir="ltr"]', multiple=True)
                        
                        if name_elem and handle_elem:
                            tweet_data['author'] = {
                                'name': name_elem[0].text,
                                'handle': handle_elem[0].text
                            }
                    
                    # Get timestamp
                    time_elem = safe_find_element(container, 'time')
                    if time_elem:
                        utc_timestamp = time_elem.get_attribute('datetime')
                        if utc_timestamp:
                            local_time_str = convert_to_local_time(utc_timestamp)
                            tweet_data['timestamp'] = local_time_str
                            tweet_data['create_time'] = local_time_str  # Alias for create_time
                            
                            # Check if tweet is within time range
                            tweet_time = datetime.strptime(local_time_str, '%Y-%m-%d %H:%M:%S')
                            
                            if start_time and tweet_time < start_time:
                                # Tweet is too old, stop crawling
                                print(f"\nReached tweets older than start_time: {start_time}")
                                return tweets
                            
                            if end_time and tweet_time > end_time:
                                # Tweet is too new, skip it
                                continue
                    
                    # Get engagement metrics
                    metrics = {}
                    metric_elements = safe_find_element(container, '[data-testid="app-text-transition-container"]', multiple=True)
                    if metric_elements and len(metric_elements) >= 4:
                        try:
                            metrics['replies'] = metric_elements[0].text if len(metric_elements) > 0 else '0'
                            metrics['reposts'] = metric_elements[1].text if len(metric_elements) > 1 else '0'
                            metrics['likes'] = metric_elements[2].text if len(metric_elements) > 2 else '0'
                            metrics['bookmarks'] = metric_elements[3].text if len(metric_elements) > 3 else '0'
                        except:
                            pass
                    tweet_data['metrics'] = metrics
                    
                    # Check for media
                    media_container = safe_find_element(container, '[data-testid="tweetPhoto"]')
                    if media_container:
                        tweet_data['has_media'] = True
                        media_elements = safe_find_element(media_container, 'img', multiple=True)
                        tweet_data['media_urls'] = [img.get_attribute('src') for img in media_elements if img.get_attribute('src')]
                    else:
                        tweet_data['has_media'] = False
                        tweet_data['media_urls'] = []
                    
                    tweet_data['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    
                    # Only add if we have essential data
                    if all(key in tweet_data for key in ['tweet_id', 'text', 'timestamp']):
                        tweets.append(tweet_data)
                        processed_tweet_ids.add(tweet_id)
                        new_tweets_found += 1
                        print(f"\rTweets extracted: {len(tweets)}", end="", flush=True)
                
                except Exception as e:
                    if "stale element reference" not in str(e):
                        print(f"\nError processing tweet: {e}")
                    continue
            
            if new_tweets_found == 0:
                consecutive_no_new_tweets += 1
            else:
                consecutive_no_new_tweets = 0
            
            # Scroll down
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)  # Wait for content to load
            
            # Calculate new scroll height
            new_height = driver.execute_script("return document.body.scrollHeight")
            
            # Break if no more new content
            if new_height == last_height:
                break
                
            last_height = new_height
            scrolls += 1
        
        print(f"\nSuccessfully extracted {len(tweets)} tweets")
        return tweets
    
    except Exception as e:
        print(f"Error extracting tweets: {e}")
        return tweets

def crawl_user_timeline(user_id, start_time=None, end_time=None):
    """
    Crawl tweets from a user's timeline within specified time range
    
    Args:
        user_id (str): The user ID or username to crawl
        start_time (datetime): Start time for tweet filtering
        end_time (datetime): End time for tweet filtering
        
    Returns:
        list: List of tweet data dictionaries
    """
    try:
        # Get or initialize browser
        driver, wait = TwitterCrawler.get_browser()
        
        # Navigate to user profile
        user_url = f'https://x.com/{user_id}'
        print(f"Navigating to {user_url}")
        driver.get(user_url)
        time.sleep(5)  # Wait for content to load
        
        # Extract tweets from timeline
        tweets = extract_tweet_data_from_timeline(driver, wait, start_time, end_time)
        
        if tweets:
            # Create user_tweets directory if it doesn't exist
            user_tweets_dir = os.path.join(os.path.dirname(__file__), 'user_tweets')
            os.makedirs(user_tweets_dir, exist_ok=True)
            
            # Save to JSON file using user ID and timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            json_path = os.path.join(user_tweets_dir, f'tweets_{user_id}_{timestamp}.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(tweets, f, ensure_ascii=False, indent=4)
            print(f"Tweet data saved to {json_path}")
            
        return tweets
    
    except Exception as e:
        print(f"Error crawling user {user_id}: {e}")
        return []

def crawl_multiple_users(user_ids, start_time=None, end_time=None):
    """
    Crawl tweets from multiple users within specified time range
    
    Args:
        user_ids (list): List of user IDs to crawl
        start_time (datetime): Start time for tweet filtering
        end_time (datetime): End time for tweet filtering
        
    Returns:
        dict: Dictionary mapping user_id to list of tweets
    """
    all_tweets = {}
    
    try:
        for i, user_id in enumerate(user_ids, 1):
            print(f"\n[{i}/{len(user_ids)}] Crawling user: {user_id}")
            tweets = crawl_user_timeline(user_id, start_time, end_time)
            all_tweets[user_id] = tweets
            print(f"Found {len(tweets)} tweets for user {user_id}")
            
            # Add delay between users to avoid rate limiting
            if i < len(user_ids):
                print("Waiting 5 seconds before next user...")
                time.sleep(5)
        
        # Save combined results
        if all_tweets:
            user_tweets_dir = os.path.join(os.path.dirname(__file__), 'user_tweets')
            os.makedirs(user_tweets_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            combined_path = os.path.join(user_tweets_dir, f'combined_tweets_{timestamp}.json')
            with open(combined_path, 'w', encoding='utf-8') as f:
                json.dump(all_tweets, f, ensure_ascii=False, indent=4)
            print(f"\nCombined results saved to {combined_path}")
        
        return all_tweets
    
    except Exception as e:
        print(f"Error in crawl_multiple_users: {e}")
        return all_tweets

def main():
    parser = argparse.ArgumentParser(description='Crawl tweets from user timelines')
    parser.add_argument('--user_ids', nargs='+', required=True, help='List of user IDs to crawl')
    parser.add_argument('--start_time', type=str, help='Start time (YYYY-MM-DD HH:MM:SS or YYYY-MM-DD)')
    parser.add_argument('--end_time', type=str, help='End time (YYYY-MM-DD HH:MM:SS or YYYY-MM-DD)')
    
    args = parser.parse_args()
    
    # Parse time arguments
    start_time = None
    end_time = None
    
    if args.start_time:
        start_time = parse_datetime(args.start_time)
        print(f"Start time: {start_time}")
    
    if args.end_time:
        end_time = parse_datetime(args.end_time)
        print(f"End time: {end_time}")
    
    try:
        # Crawl tweets from all specified users
        results = crawl_multiple_users(args.user_ids, start_time, end_time)
        
        # Print summary
        total_tweets = sum(len(tweets) for tweets in results.values())
        print(f"\n=== CRAWLING SUMMARY ===")
        print(f"Users crawled: {len(args.user_ids)}")
        print(f"Total tweets found: {total_tweets}")
        for user_id, tweets in results.items():
            print(f"  {user_id}: {len(tweets)} tweets")
    
    finally:
        # Clean up browser when done
        TwitterCrawler.close_browser()

if __name__ == "__main__":
    main()
